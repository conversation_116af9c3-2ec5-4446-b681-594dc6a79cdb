# Use the official PHP image with Apache
FROM php:7.4-apache

# Install PHP extensions needed for CI3
RUN docker-php-ext-install mysqli pdo pdo_mysql

# Enable Apache rewrite module (needed for CI3 URLs)
RUN a2enmod rewrite

# Copy app files to web root
COPY . /var/www/html/

# Set working directory
WORKDIR /var/www/html/

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Copy custom virtual host config (optional)
COPY vhost.conf /etc/apache2/sites-available/000-default.conf
