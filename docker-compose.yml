# version: '3.8'

# services:
#   web:
#     build: .
#     container_name: ci3-web
#     ports:
#       - "8000:80"
#     volumes:
#       - .:/var/www/html
#     depends_on:
#       - db

#   db:
#     image: mysql:5.7
#     container_name: ci3-db
#     restart: always
#     environment:
#       MYSQL_ROOT_PASSWORD: rootpass
#       # MYSQL_DATABASE: ci3db
#       # MYSQL_USER: ci3user
#       # MYSQL_PASSWORD: ci3pass
#     ports:
#       - "3307:3306"
#     command: --sql-mode="STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION"

#   phpmyadmin:
#     image: phpmyadmin/phpmyadmin
#     container_name: ci3-phpmyadmin
#     restart: always
#     ports:
#       - "8081:80"
#     environment:
#       PMA_HOST: db
#       MYSQL_ROOT_PASSWORD: rootpass






# version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - mysql

  mysql:
    image: mysql:5.7
    restart: always
    environment:
      MYSQL_DATABASE: ci3db
      # MYSQL_USER: ciuser
      # MYSQL_PASSWORD: cipass
      # MYSQL_ROOT_PASSWORD: rootpass
    ports:
      - "3307:3306"
    volumes:
      - db_data:/var/lib/mysql
    command: --sql-mode="STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION"

volumes:
  db_data:

